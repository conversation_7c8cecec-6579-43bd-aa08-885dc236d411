syntax = "proto3";

package codec.out.automation_report;

message AutomationReportData {
  int32 auto_idx = 1;          // Índice da automação
  uint64 start_time = 2;       // Horário do início
  uint64 restart_time = 3;     // <PERSON><PERSON><PERSON><PERSON> do reinicio, 0 se não ocorreu reinicio
  uint64 end_time = 4;         // Hor<PERSON><PERSON> do término, 0 se não finalizou
  int32 status = 5;            // Status de execução
}

message AutomationReportPackage {
  repeated AutomationReportData data = 1;  // Relatório da automação
}
