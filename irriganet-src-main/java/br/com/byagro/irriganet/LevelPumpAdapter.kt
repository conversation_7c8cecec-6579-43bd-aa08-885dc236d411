package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.databinding.ItemModelLevelPumpBinding

class LevelPumpAdapter(
    private val items: MutableList<LevelPumpItem>
) :
    RecyclerView.Adapter<LevelPumpAdapter.LevelPumpViewHolder>() {

    inner class LevelPumpViewHolder(val binding: ItemModelLevelPumpBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: LevelPumpItem, position: Int) {
            binding.checkBoxLevelPump.setOnCheckedChangeListener(null)
            binding.checkBoxLevelPump.isChecked = item.selected
            binding.checkBoxLevelPump.setText(item.name)

            binding.checkBoxLevelPump.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    updateSelection(position)
                } else {
                    item.selected = false
                    notifyItemChanged(position)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LevelPumpViewHolder {
        val binding = ItemModelLevelPumpBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return LevelPumpViewHolder(binding)
    }

    override fun onBindViewHolder(holder: LevelPumpViewHolder, position: Int) {
        holder.bind(items[position], position)
    }

    override fun getItemCount() = items.size

    private fun updateSelection(selectedPosition: Int) {
        items.forEach { it.selected = false }
        items[selectedPosition].selected = true
        notifyDataSetChanged()
    }
}