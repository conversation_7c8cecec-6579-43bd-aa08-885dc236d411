package br.com.byagro.irriganet

import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicReference

object SharedData {

    var codecWifiIsConnected = AtomicBoolean(false)
    var codecId = AtomicReference<String?>(null)
    var latestResponse = AtomicReference<String?>(null)

}