package br.com.byagro.irriganet.ui

import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.databinding.FragmentCodecConfigBinding
import br.com.byagro.irriganet.databinding.FragmentMeshDeviceConfigBinding

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [CodecConfigFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class CodecConfigFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentCodecConfigBinding
    private lateinit var sharedPref: SharedPreferences
    private lateinit var dbHelper: DBHelper

    private var codecIdx: Int = 0
    private var identity: String = ""
    private var unlockCodecIdentity: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            codecIdx = it.getInt("codecIdx")
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentCodecConfigBinding.inflate(inflater, container, false)

        try {
            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            val codec = dbHelper.getCodecByIdx(codecIdx)

            identity = codec?.get("identity") as? String ?: ""
            binding.fragCodecConfigTextInputName.setText(codec?.get("name") as? String ?: "")
            binding.fragCodecConfigTextInputIdentity.setText(identity)
            binding.fragCodecConfigTextInputWifiSsid.setText(
                codec?.get("wifi_ssid") as? String ?: ""
            )
            binding.fragCodecConfigTextInputWifiPasswd.setText(
                codec?.get("wifi_passwd") as? String ?: ""
            )

            //binding.fragCodecConfigTextInputIdentity.isEnabled = false
            binding.fragCodecConfigTextInputIdentity.isFocusable = false
            binding.fragCodecConfigTextInputIdentity.isClickable = true
            binding.fragCodecConfigTextInputIdentity.alpha = 0.6f

            binding.fragCodecConfigSwitchEnabled.isChecked =
                (codec?.get("enabled") as? Int ?: 1) == 1

            binding.fragCodecConfigTextInputIdentity.setOnClickListener {
                unlockCodecIdentity += 1
                if(unlockCodecIdentity > 5){
                    binding.fragCodecConfigTextInputIdentity.apply {
                        isFocusable = true
                        isFocusableInTouchMode = true
                        isClickable = true
                        isEnabled = true
                        alpha = 1f
                        requestFocus()
                    }
                }
            }

            binding.fragCodecConfigFabDeleteCodec.setOnClickListener {
                val builder = AlertDialog.Builder(requireContext())
                builder.setTitle("Confirmar Exclusão")
                builder.setMessage("Tem certeza de que deseja excluir este Codec? Todos os grupos e dispositivos vinculados a ele também serão removidos permanentemente.")

                builder.setPositiveButton("Sim") { dialog, _ ->
                    dbHelper.deleteCodec(codecIdx) // Delete from database
                    dialog.dismiss()
                    findNavController().popBackStack()
                }

                builder.setNegativeButton("Cancelar") { dialog, _ ->
                    dialog.dismiss() // Close dialog without deleting
                }

                val alertDialog = builder.create()
                alertDialog.show()
            }

            binding.fragCodecConfigFabSaveCodec.setOnClickListener {
                val name = binding.fragCodecConfigTextInputName.text.toString().trim()
                val identity = binding.fragCodecConfigTextInputIdentity.text.toString().trim()
                val wifiSsid = binding.fragCodecConfigTextInputWifiSsid.text.toString().trim()
                val wifiPasswd = binding.fragCodecConfigTextInputWifiPasswd.text.toString().trim()
                val enabled = binding.fragCodecConfigSwitchEnabled.isChecked
                dbHelper.updateCodec(
                    codecIdx,
                    identity,
                    name,
                    wifiSsid,
                    wifiPasswd,
                    if (enabled) 1 else 0
                )

                val timeStamp = System.currentTimeMillis() / 1000
                dbHelper.updateCodecFields(
                    codecIdx ?: 0, mapOf(
                        "last_config_update" to timeStamp
                    )
                )
                with(sharedPref.edit()) {
                    putBoolean("dataUpdated", true)
                    putBoolean("dataUpdateEvent", true)
                    apply()
                }
                findNavController().popBackStack()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return binding.root
    }
}