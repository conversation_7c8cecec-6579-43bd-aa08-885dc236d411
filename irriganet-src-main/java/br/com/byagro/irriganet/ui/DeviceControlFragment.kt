package br.com.byagro.irriganet.ui

import HiveMqttManager
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.MainActivity
import br.com.byagro.irriganet.MainActivity.LatestCodecUpdates
//import br.com.byagro.irriganet.MqttManager
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.SharedData
import br.com.byagro.irriganet.Utils
import br.com.byagro.irriganet.WebService
import br.com.byagro.irriganet.databinding.FragmentDeviceControlBinding
import codec.`in`.IncomingPacketOuterClass
import codec.`in`.control.Control
import codec.`in`.request_info.RequestInfo
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [DeviceControlFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class DeviceControlFragment : Fragment() {
    private lateinit var binding: FragmentDeviceControlBinding
    private lateinit var dbHelper: DBHelper

    private var deviceIdx: Int = -1
    private var deviceOrdIdx: Int? = null
    private var mqttManager: HiveMqttManager? = null
    private lateinit var device: Map<String, Any>
    private var job: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            deviceIdx = it.getInt("deviceIdx", -1)
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentDeviceControlBinding.inflate(inflater, container, false)

        try {
            val activity = activity as? MainActivity
            mqttManager = activity?.getMqttManager()
            device = dbHelper.getDeviceByIdx(deviceIdx)!!
            val codecIdentity = device?.get("codec_identity") as String ?: ""
            val type = device?.get("type") as Int ?: 0
            val map = device?.let {
                deviceOrdIdx = (it["ord_idx"] as? Number)?.toInt()
            }

            binding.fragDeviceControlButtonTurnOn.setOnClickListener {
                val workingTime = binding.fragDeviceControlTextWorkingTime.text.toString()
                if (workingTime.isEmpty()) {
                    Toast.makeText(
                        requireContext(),
                        "Preencha a duração!",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }
                if (deviceIdx != -1) {
                    val map = device?.let {
                        val idx = deviceOrdIdx
                        if (idx != null) {
                            val control = Control.ControlPackage.newBuilder()
                                .setIdx(idx)
                                .setAction(Control.MsgAction.MSG_TURN_ON)
                                .setWorkingTime(workingTime.toInt())
                                .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                .setId(System.currentTimeMillis() / 1000)
                                .setControl(control)
                                .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                            val finalPayload = payload + crcBytes

                            val topic = "/codec/$codecIdentity/downlink"
                            if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == codecIdentity) {
                                WebService.Request(codecIdentity, finalPayload, requireActivity(), ::handleResponse)
                            } else if (mqttManager?.isConnected() == true) {
                                lifecycleScope.launch(Dispatchers.IO) {
                                    if(mqttManager?.publish(topic, finalPayload) == true) {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Comando enviado com sucesso.", Toast.LENGTH_SHORT).show()
                                        }
                                    } else {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Erro ao enviar o comando!", Toast.LENGTH_LONG).show()
                                        }
                                    }
                                }
                            } else {
                                requireActivity().runOnUiThread {
                                    Toast.makeText(requireContext(), "Falha no conexão com o servidor!", Toast.LENGTH_LONG).show()
                                }
                            }
                        } else {
                            null
                        }
                    }


                }
            }

            binding.fragDeviceControlButtonTurnOff.setOnClickListener {
                if (deviceIdx != -1) {
                    val map = device?.let {
                        val idx = deviceOrdIdx
                        if (idx != null) {
                            val control = Control.ControlPackage.newBuilder()
                                .setIdx(idx)
                                .setAction(Control.MsgAction.MSG_TURN_OFF)
                                .setWorkingTime(0)
                                .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                .setId(System.currentTimeMillis() / 1000)
                                .setControl(control)
                                .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                            val finalPayload = payload + crcBytes

                            val topic = "/codec/$codecIdentity/downlink"
                            if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == codecIdentity) {
                                WebService.Request(codecIdentity, finalPayload, requireActivity(), ::handleResponse)
                            } else if (mqttManager?.isConnected() == true) {
                                lifecycleScope.launch(Dispatchers.IO) {
                                    if(mqttManager?.publish(topic, finalPayload) == true) {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Comando enviado com sucesso.", Toast.LENGTH_SHORT).show()
                                        }
                                    } else {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Erro ao enviar o comando!", Toast.LENGTH_LONG).show()
                                        }
                                    }
                                }
                            } else {
                                requireActivity().runOnUiThread {
                                    Toast.makeText(requireContext(), "Falha no conexão com o servidor!", Toast.LENGTH_LONG).show()
                                }
                            }
                        } else {
                            null
                        }
                    }
                }
            }

            job = CoroutineScope(Dispatchers.IO).launch {
                while (true) {
                    withContext(Dispatchers.Main) {
                        updateStatusUI()
                    }
                    delay(2_000)
                }
            }
        } catch (e: Exception){
            e.printStackTrace()
        }

        return binding.root
    }

    private fun updateStatusUI() {
        try {
            val act      = activity as? MainActivity ?: return
            val identity = device["codec_identity"] as? String ?: return
            val idx      = deviceOrdIdx ?: return
            val updates  = act.getLatestCodecsUpdates()[identity] ?: return

            val mask     = 1L shl idx

            val transmitting = updates.syncBitmask?.let { it and mask != 0L }
            binding.fragDeviceControlStatusTransmittingTextView.text = if (transmitting?: false) "Transmitindo" else "Fora da Rede"

            val poweredOn = updates.onBitmask?.let { it and mask != 0L }
            binding.fragDeviceControlStatusPowerTextView.text = if (poweredOn?: false) "Ligado" else "Desligado"

            val inputHigh = updates.inputBitmask?.let { it and mask != 0L }
            binding.fragDeviceControlStatusInputTextView.text = if (inputHigh?: false) "Entrada Ligada" else "Entrada Deslig."

            if(transmitting != null){
                if (transmitting == true) {
                    binding.fragDeviceControlStatusTransmittingImageView.setColorFilter(Color.parseColor("#4CAF50"))
                } else {
                    binding.fragDeviceControlStatusTransmittingImageView.setColorFilter(Color.parseColor("#FFEB3B"))
                }
            } else {
                binding.fragDeviceControlStatusTransmittingImageView.setColorFilter(Color.parseColor("#BDBDBD"))
            }

            if(poweredOn != null){
                if (poweredOn == true) {
                    binding.fragDeviceControlStatusPowerImageView.setColorFilter(Color.parseColor("#4CAF50"))
                } else {
                    binding.fragDeviceControlStatusPowerImageView.setColorFilter(Color.parseColor("#000000"))
                }
            } else {
                binding.fragDeviceControlStatusPowerImageView.setColorFilter(Color.parseColor("#BDBDBD"))
            }

            if(inputHigh != null){
                if (inputHigh == true) {
                    binding.fragDeviceControlStatusInputImageView.setColorFilter(Color.parseColor("#4CAF50"))
                } else {
                    binding.fragDeviceControlStatusInputImageView.setColorFilter(Color.parseColor("#000000"))
                }
            } else {
                binding.fragDeviceControlStatusInputImageView.setColorFilter(Color.parseColor("#BDBDBD"))
            }
        } catch (e: Exception){
            e.printStackTrace()
        }
    }

    fun handleResponse(identity: String?, response: ByteArray?) {

    }

    override fun onPause() {
        super.onPause()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        job?.cancel()
    }
}