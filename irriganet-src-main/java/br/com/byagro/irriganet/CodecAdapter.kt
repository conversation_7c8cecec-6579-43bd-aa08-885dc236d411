package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class CodecAdapter(
    private val codecList: MutableList<CodecItem>,
    private val onItemClick: (CodecItem) -> Unit,
    private val onConfigClick: (Int) -> Unit
) :
    RecyclerView.Adapter<CodecAdapter.CodecViewHolder>() {

    class CodecViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textView: TextView = itemView.findViewById(R.id.item_text)
        val configConfig: ImageButton = itemView.findViewById(R.id.btn_config)

        fun bind(item: CodecItem, onItemClick: (CodecItem) -> Unit, onConfigClick: (Int) -> Unit) {
            itemView.setOnClickListener {
                onItemClick(item)
            }

            configConfig.setOnClickListener {
                onConfigClick(adapterPosition)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CodecViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_model_text_btn_conf, parent, false)
        return CodecViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: CodecViewHolder, position: Int) {
        val currentCodec = codecList[position]
        holder.textView.text = currentCodec.identity
        holder.bind(currentCodec, onItemClick, onConfigClick)
    }

    override fun getItemCount() = codecList.size
}