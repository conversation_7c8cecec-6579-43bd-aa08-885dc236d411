package br.com.byagro.irriganet

import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.databinding.ItemModelSectorSchedulingBinding
import java.util.Collections

class SectorSchedulingAdapter(
    private val items: MutableList<SectorSchedulingItem>,
    private var showFerti: Boolean
) :
    RecyclerView.Adapter<SectorSchedulingAdapter.SchedulingViewHolder>() {

    class SchedulingViewHolder(val binding: ItemModelSectorSchedulingBinding) : RecyclerView.ViewHolder(binding.root) {

        var durationWatcher: TextWatcher? = null
        var fertiWatcher: TextWatcher? = null
        var fertiWatcherDelay: TextWatcher? = null

        fun bind(item: SectorSchedulingItem, showFerti: Boolean) {

            if (showFerti) {
                binding.textInputLayoutFerti.visibility = View.VISIBLE
                binding.textInputLayoutFertiDelay.visibility = View.VISIBLE
                binding.textInputFerti.setText(item.ferti)
            } else {
                binding.textInputLayoutFerti.visibility = View.GONE
                binding.textInputLayoutFertiDelay.visibility = View.GONE
            }

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SchedulingViewHolder {
        val binding = ItemModelSectorSchedulingBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SchedulingViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SchedulingViewHolder, position: Int) {
        //holder.setIsRecyclable(false);
        var item = items[position]
        holder.binding.checkBoxSetor.setOnCheckedChangeListener(null)
        holder.binding.textInputDuration.removeTextChangedListener(holder.durationWatcher)
        holder.binding.textInputFerti.removeTextChangedListener(holder.fertiWatcher)

        holder.binding.checkBoxSetor.isChecked = item.enabled
        holder.binding.checkBoxSetor.text = "Setor ${item.sector}"

        if (holder.binding.textInputDuration.text.toString() != item.duration) {
            holder.binding.textInputDuration.setText(item.duration)
        }
        if (holder.binding.textInputFerti.text.toString() != item.ferti) {
            holder.binding.textInputFerti.setText(item.ferti)
        }
        if (holder.binding.textInputFertiDelay.text.toString() != item.fertiDelay) {
            holder.binding.textInputFertiDelay.setText(item.fertiDelay)
        }

        holder.durationWatcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val currentPosition = holder.adapterPosition
                if (currentPosition != RecyclerView.NO_POSITION) {
                    items[currentPosition].duration = s.toString()
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        holder.fertiWatcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val currentPosition = holder.adapterPosition
                if (currentPosition != RecyclerView.NO_POSITION) {
                    items[currentPosition].ferti = s.toString()
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        holder.fertiWatcherDelay = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val currentPosition = holder.adapterPosition
                if (currentPosition != RecyclerView.NO_POSITION) {
                    items[currentPosition].fertiDelay = s.toString()
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        holder.binding.textInputDuration.addTextChangedListener(holder.durationWatcher)
        holder.binding.textInputFerti.addTextChangedListener(holder.fertiWatcher)
        holder.binding.textInputFertiDelay.addTextChangedListener(holder.fertiWatcherDelay)
        holder.binding.checkBoxSetor.setOnCheckedChangeListener { _, isChecked ->
            items[position].enabled = isChecked
        }

        holder.bind(items[position], showFerti)
    }

    fun moverItem(fromPosition: Int, toPosition: Int) {
        val itemMovido = items.removeAt(fromPosition)
        items.add(toPosition, itemMovido)
        notifyItemMoved(fromPosition, toPosition)
    }

    fun setShowFerti(show: Boolean) {
        if (showFerti != show) {
            showFerti = show
            notifyDataSetChanged()
        }
    }

    override fun getItemCount() = items.size

}