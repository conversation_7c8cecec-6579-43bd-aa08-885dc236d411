package br.com.byagro.irriganet

import android.content.Context
import android.util.Log
import com.android.volley.*
import com.android.volley.toolbox.JsonObjectRequest
import com.android.volley.toolbox.StringRequest
import com.android.volley.toolbox.Volley
import org.json.JSONObject
import android.util.Base64;
import codec.out.OutgoingPacketOuterClass
import com.android.volley.toolbox.HttpHeaderParser

object WebService {

    private const val USERNAME = "byagro"
    private const val PASSWORD = "i8dEYH7tcNxVf18"

    private fun getAuthHeader(): String {
        val credentials = "$USERNAME:$PASSWORD"
        return "Basic " + Base64.encodeToString(credentials.toByteArray(), Base64.NO_WRAP)
    }

    fun Request(jsonObject: JSONObject, context: Context) {
        try {
            val queue: RequestQueue = Volley.newRequestQueue(context)
            val url = "http://192.168.4.1/data"

            val request = object : JsonObjectRequest(
                Request.Method.POST, url, jsonObject,
                { response ->
                    Log.d("VolleyResponse", response.toString())
                },
                { error ->
                    error.printStackTrace()
                    Log.e("VolleyError", error.message.toString())
                }
            ) {
                override fun getHeaders(): MutableMap<String, String> {
                    return hashMapOf(
                        "Content-Type" to "application/json",
                        "Authorization" to getAuthHeader()
                    )
                }
            }

            queue.add(request)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun Request(data: String, context: Context) {
        try {
            val queue: RequestQueue = Volley.newRequestQueue(context)
            val url = "http://192.168.4.1/data"

            val request = object : StringRequest(
                Method.POST, url,
                { response ->
                    Log.d("VolleyResponse", response)
                },
                { error ->
                    error.printStackTrace()
                    Log.e("VolleyError", error.message.toString())
                }
            ) {
                override fun getHeaders(): MutableMap<String, String> {
                    return hashMapOf(
                        "Content-Type" to "application/x-www-form-urlencoded",
                        "Authorization" to getAuthHeader()
                    )
                }

                override fun getBody(): ByteArray {
                    return data.toByteArray(Charsets.UTF_8)
                }

                override fun getBodyContentType(): String {
                    return "application/x-www-form-urlencoded; charset=UTF-8"
                }
            }

            queue.add(request)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun Request(identity: String, data: String, context: Context, callback: (String?,String?) -> Unit) {
        try {
            val queue = Volley.newRequestQueue(context)
            val url = "http://192.168.4.1/data"

            val request = object : StringRequest(
                Request.Method.POST, url,
                { response ->
                    Log.d("VolleyResponse", response.toString())
                    callback(response.toString(), identity)
                },
                { error ->
                    error.printStackTrace()
                    Log.e("VolleyError", error.message ?: "Unknown error")
                }
            ) {
                override fun getHeaders(): MutableMap<String, String> {
                    return hashMapOf(
                        "Content-Type" to "application/x-www-form-urlencoded",
                        "Authorization" to getAuthHeader()
                    )
                }

                override fun getBody(): ByteArray {
                    return data.toByteArray(Charsets.UTF_8)
                }

                override fun getBodyContentType(): String {
                    return "application/x-www-form-urlencoded; charset=UTF-8"
                }
            }

            queue.add(request)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun Request(identity: String, data: ByteArray, context: Context, callback: (String?, ByteArray?) -> Unit) {
        try {
            val queue = Volley.newRequestQueue(context)
            val url = "http://192.168.4.1/data"

            val request = object : StringRequest(
                Request.Method.POST, url,
                { response ->
                    Log.d("VolleyResponse", response.toString())
                    callback(identity, response.toByteArray())
                },
                { error ->
                    error.printStackTrace()
                    Log.e("VolleyError", error.message ?: "Unknown error")
                }
            ) {
                override fun getHeaders(): MutableMap<String, String> {
                    return hashMapOf(
                        "Content-Type" to "application/octet-stream",
                        "Authorization" to getAuthHeader()
                    )
                }

                override fun getBody(): ByteArray {
                    return data
                }

                override fun getBodyContentType(): String {
                    return "application/octet-stream"
                }
            }

            queue.add(request)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun Request(jsonObject: JSONObject, context: Context, callback: (JSONObject?) -> Unit) {
        try {
            val queue = Volley.newRequestQueue(context)
            val url = "http://192.168.4.1/data"

            val request = object : JsonObjectRequest(
                Request.Method.POST, url, jsonObject,
                { response ->
                    Log.d("VolleyResponse", response.toString())
                    callback(response)
                },
                { error ->
                    error.printStackTrace()
                    Log.e("VolleyError", error.message ?: "Unknown error")
                }
            ) {
                override fun getHeaders(): Map<String, String> {
                    return mapOf("Authorization" to getAuthHeader())
                }
            }

            queue.add(request)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getCodecInfo(context: Context, callback: (JSONObject?) -> Unit) {
        try {
            val queue = Volley.newRequestQueue(context.applicationContext)
            val url = "http://192.168.4.1/info"

            val request = object : JsonObjectRequest(
                Request.Method.GET, url, null,
                { response ->
                    Log.d("VolleyResponse", response.toString())
                    callback(response)
                },
                { error ->
                    error.printStackTrace()
                    Log.e("VolleyError", error.message ?: "Unknown error")
                    callback(null)
                }
            ) {
                override fun getHeaders(): Map<String, String> {
                    val authHeader = getAuthHeader()
                    Log.d("AuthDebug", "Header: $authHeader") // Log para depuração
                    return mapOf("Authorization" to authHeader)
                }
            }

            request.setShouldCache(false)
            request.retryPolicy = DefaultRetryPolicy(
                3000,
                DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
            )

            queue.add(request)
        } catch (e: Exception) {
            e.printStackTrace()
            callback(null)
        }
    }

    /*fun getCodecInfo(context: Context, callback: (String?, ByteArray?) -> Unit) {
        val queue = Volley.newRequestQueue(context.applicationContext)
        val url   = "http://192.168.4.1/info"

        val request = object : Request<ByteArray>(Method.GET, url,
            Response.ErrorListener { error ->
                //error.printStackTrace()
            }
        ) {
            override fun getHeaders(): MutableMap<String, String> = mutableMapOf("Authorization" to getAuthHeader())

            override fun getBodyContentType(): String = "application/x-protobuf"

            override fun parseNetworkResponse(response: NetworkResponse)
                    : Response<ByteArray> {
                return Response.success(
                    response.data,
                    HttpHeaderParser.parseCacheHeaders(response)
                )
            }

            override fun deliverResponse(response: ByteArray) {
                Log.d("Volley", "Bytes len: ${response.size}")
                callback(null, response)
            }
        }

        request.setShouldCache(false)
        request.retryPolicy = DefaultRetryPolicy(
            3000,
            DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
            DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        )

        queue.add(request)
    }*/

    fun getCodecInfo(context: Context, type: Byte, callback: (String?, ByteArray?) -> Unit) {
        val queue = Volley.newRequestQueue(context.applicationContext)
        val url   = "http://192.168.4.1/report"

        val request = object : Request<ByteArray>(Method.POST, url,
            Response.ErrorListener { error ->
                //error.printStackTrace()
            }
        ) {

            override fun getHeaders(): MutableMap<String, String> =
                mutableMapOf(
                    "Authorization" to getAuthHeader(),
                    "Content-Type"  to "application/x-protobuf"
                )

            override fun getBody(): ByteArray = byteArrayOf(type)

            override fun parseNetworkResponse(
                response: NetworkResponse
            ): Response<ByteArray> =
                Response.success(
                    response.data,
                    HttpHeaderParser.parseCacheHeaders(response)
                )

            override fun deliverResponse(response: ByteArray) {
                Log.d("Volley", "Bytes len: ${response.size}")
                callback(null, response)
            }
        }

        request.setShouldCache(false)
        request.retryPolicy = DefaultRetryPolicy(
            3000,
            DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
            DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        )

        queue.add(request)
    }
}