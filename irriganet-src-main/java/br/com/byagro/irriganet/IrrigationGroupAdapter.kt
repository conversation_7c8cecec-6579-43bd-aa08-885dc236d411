package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class IrrigationGroupAdapter(
    private val groupList: MutableList<GroupItem>,
    private val onItemClick: (GroupItem) -> Unit
) :
    RecyclerView.Adapter<IrrigationGroupAdapter.IrrigationGroupViewHolder>() {

    class IrrigationGroupViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textView: TextView = itemView.findViewById(R.id.item_text)

        fun bind(item: GroupItem, onItemClick: (GroupItem) -> Unit) {
            itemView.setOnClickListener {
                onItemClick(item)
            }

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): IrrigationGroupViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_model_simple_text, parent, false)
        return IrrigationGroupViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: IrrigationGroupViewHolder, position: Int) {
        val currentGroup = groupList[position]
        holder.textView.text = currentGroup.name
        holder.bind(currentGroup, onItemClick)
    }

    override fun getItemCount() = groupList.size
}