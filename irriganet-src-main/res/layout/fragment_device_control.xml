<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.DeviceControlFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp">

        <LinearLayout
            android:id="@+id/frag_device_control_status_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:weightSum="3"
            android:padding="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Transmissão -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <ImageView
                    android:id="@+id/frag_device_control_status_transmitting_image_view"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    app:tint="#BDBDBD"
                    android:src="@drawable/ic_transmicao" />

                <TextView
                    android:id="@+id/frag_device_control_status_transmitting_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fora da Rede"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Alimentação -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <ImageView
                    android:id="@+id/frag_device_control_status_power_image_view"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    app:tint="#BDBDBD"
                    android:src="@drawable/ic_power" />

                <TextView
                    android:id="@+id/frag_device_control_status_power_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Desligado"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Entrada digital -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <ImageView
                    android:id="@+id/frag_device_control_status_input_image_view"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    app:tint="#BDBDBD"
                    android:src="@drawable/baseline_cable_24" />

                <TextView
                    android:id="@+id/frag_device_control_status_input_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Entrada Deslig."
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_device_control_text_layout_working_time"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            app:boxStrokeColor="@color/black"
            app:hintTextAppearance="@style/CollapsedHintTextAppearance"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/frag_device_control_status_container">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_device_control_text_working_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:digits="0123456789"
                android:hint="Duração"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/frag_device_control_button_turn_on"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="LIGAR"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/frag_device_control_text_layout_working_time" />

        <Button
            android:id="@+id/frag_device_control_button_turn_off"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="DESLIGAR"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/frag_device_control_button_turn_on" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
