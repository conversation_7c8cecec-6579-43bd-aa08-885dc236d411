<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.GroupConfigFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_group_config_text_input_layout_name"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            app:boxStrokeColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_group_config_text_input_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="Nome do Grupo"
                android:inputType="textCapSentences" />
        </com.google.android.material.textfield.TextInputLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/frag_group_config_fab_delete_codec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginBottom="96dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="Apagar Talhão"
        app:srcCompat="@android:drawable/ic_menu_delete"
        app:tint="@android:color/white" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/frag_group_config_fab_save_codec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginBottom="24dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="Salvar Talhão"
        app:srcCompat="@android:drawable/ic_menu_save"
        app:tint="@android:color/white" />

</FrameLayout>