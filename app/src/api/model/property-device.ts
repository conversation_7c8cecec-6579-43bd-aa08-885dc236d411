// Auto-generated from Directus schema

import type { DirectusRelationFieldType } from "@/utils/types";
import type { Model } from "./common";
import type { Device } from "./device";
import type { MeshDeviceMapping } from "./mesh-device-mapping";
import type { Property } from "./property";

export type PropertyDeviceRelationsTypes = {
  property: DirectusRelationFieldType<Property>;
  device: DirectusRelationFieldType<Device>;
  current_mesh_device_mapping: DirectusRelationFieldType<MeshDeviceMapping>;
};

export type PropertyDeviceDefaultRelationsTypes = PropertyDeviceRelationsTypes;

export interface PropertyDevice<
  Types extends Partial<PropertyDeviceRelationsTypes> = PropertyDeviceDefaultRelationsTypes
> extends Model {
  property: Types["property"];
  device: Types["device"];
  label: string;
  start_date: string;
  end_date: string | null;
  current_mesh_device_mapping: Types["current_mesh_device_mapping"] | null;
}
