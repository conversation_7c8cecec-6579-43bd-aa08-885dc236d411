# Task list info:

- name: 250804_01
- base_branch: develop

---

# Task list context:

This task list is for planning the implementation of the MQTT integration with LIC devices.
The integration is made by exchanging protobuf messages via MQTT.
The protobuf encoding/decoding is done by the `protobuf` package and used in the `mqtt-integration` package.
The `mqtt-integration` package is responsible for connecting to the MQTT broker, subscribing to the topics, and handling the messages.

Protobuf encoding/decoding and MQTT broker connection, message receiving and dispatching are already implemented.

---

# Tasks

## Task 1. Identify what database changes will generate a new LIC configuration


**Description**
IncomingPacket declared in protobuf/proto/incoming_packet.proto is the message sent to the LIC to configure it. Its payload is one of the messages imported in the same file. For example, ConfigPackage, which is declared in protobuf/proto/config.proto.
We need to identify what changes in the database will generate a new LIC configuration and what is the corresponding protobuf message.

**Target directories**

- <directory-1> (<directory-description>)
- <directory-2> (<directory-description>)

**Status:** Pending|In Progress|Done|Canceled|Error
