# LIC Configuration Changes Analysis

This document identifies database changes that will generate new LIC configuration messages and their corresponding protobuf messages.

## Overview

The LIC (Localized Irrigation Controller) receives configuration through protobuf messages via MQTT. The `IncomingPacket` message contains different payload types that configure various aspects of the LIC operation.

## Configuration Message Types

Based on the protobuf definitions in `protobuf/proto/incoming_packet.proto`, the following configuration message types are available:

1. **ConfigPackage** - General LIC configuration
2. **DevicesPackage** - Mesh device configuration
3. **SchedulingPackage** - Irrigation scheduling configuration
4. **DeviceSchedulingPackage** - Device-specific scheduling
5. **AutomationPackage** - Automation rules configuration
6. **ControlPackage** - Direct device control
7. **PauseSchedulingPackage** - Pause/resume scheduling
8. **RequestInfoPackage** - Information requests
9. **FirmwareUpdatePackage** - Firmware updates

## Database Changes That Generate LIC Configuration

### 1. Property Configuration Changes (ConfigPackage)

**Database Table:** `property`

**Monitored Fields:**
- `backwash_duration_minutes` → `backwash_duration` (converted to seconds)
- `backwash_period_minutes` → `backwash_cycle` (converted to cycles)
- `backwash_delay_seconds` → `backwash_delay`
- `rain_gauge_enabled` → `raingauge_enabled`
- `rain_gauge_resolution_mm` → `raingauge_factor` (conversion factor)
- `precipitation_volume_limit_mm` → `rainfall_limit`
- `precipitation_suspended_duration_hours` → `rainfall_pause_duration` (converted to minutes)

**Protobuf Message:** `codec.in.config.ConfigPackage`

**Database Triggers/Functions:**
- Trigger: `trg_property_config_change` (AFTER UPDATE)
- Function: `fn_send_lic_config_update(property_id UUID)`

**Additional Configuration Fields:**
- WiFi configuration (WifiConfig) - may come from device metadata or separate configuration
- Mesh configuration (MeshConfig) - may come from mesh network settings

### 2. Mesh Device Configuration (DevicesPackage)

**Database Tables:** 
- `property_device` (for device associations)
- `mesh_device_mapping` (for mesh-LIC relationships)
- `device` (for device information)

**Monitored Fields:**
- `property_device.start_date` / `property_device.end_date` (device activation/deactivation)
- `mesh_device_mapping.start_date` / `mesh_device_mapping.end_date` (mesh associations)
- `device.model` (device type)
- `device.metadata` (device-specific configuration like mesh_id, device_id)

**Protobuf Message:** `codec.in.devices.DevicesPackage`

**DevicesData Fields Mapping:**
- `idx` → device slot index (derived from mesh configuration)
- `mesh_id` → from device metadata or mesh configuration
- `device_id` → from device metadata
- `device_type` → derived from device.model
- `out1`, `out2`, `input`, `mode` → from device operational state
- `sector` → from sector association
- `group_idx` → from project grouping
- `eqpt_ver` → from device metadata

**Database Triggers/Functions:**
- Trigger: `trg_mesh_device_mapping_config_change` (AFTER INSERT/UPDATE/DELETE)
- Trigger: `trg_property_device_config_change` (AFTER UPDATE)
- Function: `fn_send_lic_devices_update(lic_property_device_id UUID)`

### 3. Irrigation Scheduling Configuration (SchedulingPackage)

**Database Tables:**
- `irrigation_plan`
- `irrigation_plan_step`
- `sector`
- `project`

**Monitored Fields:**

**irrigation_plan:**
- `start_time` → `start_time` (converted to minutes since midnight)
- `days_of_week` → `days_of_week` (converted to bitmask)
- `is_enabled` → affects whether scheduling is sent
- `fertigation_enabled` → `allow_ferti`
- `backwash_enabled` → `allow_backwash`

**irrigation_plan_step:**
- `order` → determines scheduling sequence
- `duration_seconds` → `sector_working_time` (converted to minutes)
- `fertigation_start_delay_seconds` → `ferti_delay` (converted to minutes)
- `fertigation_duration_seconds` → `ferti_working_time` (converted to minutes)

**Protobuf Message:** `codec.in.scheduling.SchedulingPackage`

**Scheduling Fields Mapping:**
- `idx` → irrigation_plan sequence index
- `start_time` → from irrigation_plan.start_time (minutes since midnight)
- `days_of_week` → from irrigation_plan.days_of_week (bitmask conversion)
- `number_of_steps` → count of irrigation_plan_step records
- `waterpump_idx` → from project.irrigation_water_pump mapping
- `waterpump_working_time` → sum of all step durations
- `allow_ferti` → from irrigation_plan.fertigation_enabled
- `ferti_idx` → from project.fertigation_water_pump mapping
- `allow_backwash` → from irrigation_plan.backwash_enabled
- `backwash_idx` → from backwash configuration
- `group` → from project grouping

**DeviceScheduling Fields Mapping:**
- `idx` → device scheduling sequence index
- `scheduling_idx` → reference to main scheduling
- `device_idx` → from sector.valve_controller mapping
- `order` → from irrigation_plan_step.order
- `sector_working_time` → from irrigation_plan_step.duration_seconds
- `ferti_working_time` → from irrigation_plan_step.fertigation_duration_seconds
- `ferti_delay` → from irrigation_plan_step.fertigation_start_delay_seconds

**Database Triggers/Functions:**
- Trigger: `trg_irrigation_plan_config_change` (AFTER INSERT/UPDATE/DELETE)
- Trigger: `trg_irrigation_plan_step_config_change` (AFTER INSERT/UPDATE/DELETE)
- Function: `fn_send_lic_scheduling_update(project_id UUID)`

### 4. Automation Configuration (AutomationPackage)

**Database Tables:**
- `reservoir` (for level sensors and pumps)
- `water_pump` (for pump automation)

**Monitored Fields:**
- `reservoir.reservoir_monitor` → `level_idx` (level sensor device)
- `reservoir.water_pump` → `pump_idx` (associated pump)
- `reservoir.enabled` → affects automation rules
- `water_pump.monitor_operation` → automation trigger conditions

**Protobuf Message:** `codec.in.automation.AutomationPackage`

**AutomationData Fields Mapping:**
- `level_idx` → from reservoir.reservoir_monitor device mapping
- `pump_idx` → from reservoir.water_pump mapping
- `mask` → automation condition mask
- `value` → trigger threshold value
- `working_time` → pump operation duration

**Database Triggers/Functions:**
- Trigger: `trg_reservoir_automation_change` (AFTER UPDATE)
- Trigger: `trg_water_pump_automation_change` (AFTER UPDATE)
- Function: `fn_send_lic_automation_update(property_id UUID)`

## Summary of Required Database Triggers and Functions

### Triggers to Implement:

1. **Property Configuration:**
   - `trg_property_config_change` on `property` table
   
2. **Device Configuration:**
   - `trg_mesh_device_mapping_config_change` on `mesh_device_mapping` table
   - `trg_property_device_config_change` on `property_device` table
   
3. **Scheduling Configuration:**
   - `trg_irrigation_plan_config_change` on `irrigation_plan` table
   - `trg_irrigation_plan_step_config_change` on `irrigation_plan_step` table
   
4. **Automation Configuration:**
   - `trg_reservoir_automation_change` on `reservoir` table
   - `trg_water_pump_automation_change` on `water_pump` table

### Functions to Implement:

1. `fn_send_lic_config_update(property_id UUID)` - Sends ConfigPackage
2. `fn_send_lic_devices_update(lic_property_device_id UUID)` - Sends DevicesPackage
3. `fn_send_lic_scheduling_update(project_id UUID)` - Sends SchedulingPackage
4. `fn_send_lic_automation_update(property_id UUID)` - Sends AutomationPackage

### MQTT Integration:

Each function should:
1. Query the relevant database tables to gather configuration data
2. Transform the data to match protobuf message format
3. Encode the message using protobuf
4. Publish to the appropriate MQTT topic (`/codec/{device_identifier}/downlink`)

## Notes:

- Control, Pause, RequestInfo, and FirmwareUpdate packages are typically triggered by user actions rather than database changes
- Device scheduling may be sent as part of SchedulingPackage or separately as DeviceSchedulingPackage
- WiFi and Mesh configuration in ConfigPackage may require additional database tables or metadata fields
- Time conversions between database (various units) and protobuf (specific units) need careful handling
- Bitmask conversion for days_of_week requires mapping from JSON array to integer bitmask
